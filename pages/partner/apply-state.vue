<template>
  <view class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <view class="max-w-4xl mx-auto px-4 py-8">

      <!-- 申请提交成功提示 -->
      <view class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
        <view class="text-center">
          <text class="text-6xl mb-4 block">✅</text>
          <text class="text-2xl font-bold text-green-600 mb-2 block">申请提交成功！</text>
          <text class="text-gray-600 block">感谢您的申请，我们将在3个工作日内与您联系</text>
        </view>
      </view>

      <!-- 申请审核状态 -->
      <view class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
        <view class="flex items-center mb-6">
          <text class="text-2xl mr-3">📋</text>
          <text class="text-2xl font-bold text-gray-800">申请审核状态</text>
        </view>


        <!-- 当前状态详情 -->
        <view class="bg-blue-50 p-6 rounded-xl border border-blue-200">
          <view class="flex items-center mb-3">
            <text class="text-blue-600 font-semibold mr-2">当前状态：</text>
            <text
              :class="['font-bold px-3 py-1 rounded-full text-sm',
                       apply.status === 0 ? 'bg-yellow-100 text-yellow-800' :
                       apply.status === 1 ? 'bg-blue-100 text-blue-800' :
                       apply.status === 2 ? 'bg-green-100 text-green-800' :
                       'bg-red-100 text-red-800']"
            >
              {{ getStatusText(apply.status) }}
            </text>
          </view>
          <text class="text-blue-700 text-sm block">{{ getStatusDescription(apply.status) }}</text>
        </view>
      </view>

      <!-- 申请信息概览 -->
      <view class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
        <view class="flex items-center mb-6">
          <text class="text-2xl mr-3">👤</text>
          <text class="text-2xl font-bold text-gray-800">申请信息</text>
        </view>

        <view class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <view class="flex justify-between">
            <text class="text-gray-600">申请人：</text>
            <text class="font-medium text-gray-800">{{ apply.name }}</text>
          </view>
          <view class="flex justify-between">
            <text class="text-gray-600">联系电话：</text>
            <text class="font-medium text-gray-800">{{ apply.phone }}</text>
          </view>
          <view class="flex justify-between">
            <text class="text-gray-600">合作级别：</text>
            <text class="font-medium text-gray-800">{{ getPartnerLevelName(apply.partnerLevelId) }}</text>
          </view>
          <view class="flex justify-between">
            <text class="text-gray-600">所在地区：</text>
            <text class="font-medium text-gray-800">{{ apply.province }}{{ apply.city }}{{ apply.district }}</text>
          </view>
          <view class="flex justify-between md:col-span-2">
            <text class="text-gray-600">申请时间：</text>
            <text class="font-medium text-gray-800">{{ formatDate(apply.createTime) }}</text>
          </view>
        </view>
      </view>

      <!-- 押金信息 -->
      <view v-if="selectedLevel && selectedLevel.deposit > 0" class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
        <view class="flex items-center mb-6">
          <text class="text-2xl mr-3">💰</text>
          <text class="text-2xl font-bold text-gray-800">押金信息</text>
        </view>

        <view class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <view class="text-center p-4 bg-blue-50 rounded-xl">
            <text class="text-blue-600 text-sm block mb-2">应缴押金</text>
            <text class="text-2xl font-bold text-blue-800">{{ selectedLevel.deposit }}元</text>
          </view>
          <view class="text-center p-4 bg-green-50 rounded-xl">
            <text class="text-green-600 text-sm block mb-2">已缴押金</text>
            <text class="text-2xl font-bold text-green-800">{{ apply.paidDeposit || 0 }}元</text>
          </view>
          <view class="text-center p-4 bg-orange-50 rounded-xl">
            <text class="text-orange-600 text-sm block mb-2">待缴押金</text>
            <text class="text-2xl font-bold text-orange-800">{{ selectedLevel.deposit - (apply.paidDeposit || 0) }}元</text>
          </view>
        </view>

        <!-- 押金缴纳方式 -->
        <view v-if="apply.depositPaymentMethod" class="bg-gray-50 p-4 rounded-xl">
          <text class="text-gray-600 text-sm block mb-2">选择的缴纳方式：</text>
          <text class="font-medium text-gray-800">
            {{ apply.depositPaymentMethod === 'once' ? '一次性缴纳' : '分期（返点抵扣）' }}
          </text>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="space-y-4">
        <!-- 审核通过且需要缴纳押金 -->
        <view v-if="apply.status === 2 && selectedLevel && selectedLevel.deposit > 0 && (apply.paidDeposit || 0) < selectedLevel.deposit">
          <button
            @tap="handlePayDeposit"
            class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
          >
            缴纳押金 ({{ selectedLevel.deposit - (apply.paidDeposit || 0) }}元)
          </button>
        </view>

        <!-- 审核中或审核失败时显示修改申请按钮 -->
        <view v-if="apply.status === 0 || apply.status === 3">
          <button
            @tap="handleEditApplication"
            class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
          >
            返回修改申请
          </button>
        </view>

        <!-- 联系客服按钮 -->
        <button
          @tap="handleContactService"
          class="w-full bg-white border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-xl text-lg font-semibold hover:border-gray-400 transition-all duration-300"
        >
          联系客服
        </button>
      </view>

      <!-- 联系信息 -->
      <view class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl shadow-xl p-8 text-white mt-8">
        <text class="text-2xl font-bold mb-6 text-center block">联系方式</text>
        <view class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
          <view>
            <text class="font-semibold mb-2 block">咨询热线</text>
            <text class="block">13296653558</text>
            <text class="text-blue-200 block">工作日 9:00-18:00</text>
          </view>
          <view>
            <text class="font-semibold mb-2 block">咨询邮箱</text>
            <text class="block"><EMAIL></text>
          </view>
          <view class="md:col-span-2">
            <text class="font-semibold mb-2 block">联系地址</text>
            <text class="block">安徽省合肥市肥东县肥东新城开发区临泉东路与护城路爱就爱工业园区12栋</text>
            <text class="block">安徽合肥喜运来家具有限公司四楼办公室</text>
            <text class="block">介先生 13618687676</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {ref, computed} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerApplyData} from "@/common/api/partner"

const apply = ref<any>({})

// 合伙人级别配置（与apply.vue保持一致）
const partnerLevels = ref([
  {
    id: '1',
    name: '资源引荐官',
    deposit: 0,
    commission: '最高15%（一次性）',
    features: ['无门槛', '一次性分成', '仅对接资源']
  },
  {
    id: '2',
    name: 'T1级超级合伙人',
    deposit: 5000,
    commission: '9%（持续）',
    features: ['5000元押金', '持续性分成', '兼职拓展']
  },
  {
    id: '3',
    name: 'T2级超级合伙人',
    deposit: 10000,
    commission: '12%（持续）',
    features: ['10000元押金', '持续性分成', '全职运营']
  },
  {
    id: '4',
    name: 'T3级超级合伙人',
    deposit: 50000,
    commission: '15%（持续）+ 年度奖励',
    features: ['50000元押金', '持续性分成', '团队化运作']
  }
])

// 状态步骤配置
const statusSteps = ref([
  { name: '提交申请', time: '' },
  { name: '资料审核', time: '' },
  { name: '审核通过', time: '' },
  { name: '缴纳押金', time: '' },
  { name: '合作开始', time: '' }
])

// 计算选中的级别信息
const selectedLevel = computed(() => {
  return partnerLevels.value.find(level => level.id === apply.value.partnerLevelId)
})

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    0: '待审核',
    1: '审核中',
    2: '审核通过',
    3: '审核失败',
  }
  return statusMap[status] || '未知状态'
}

// 获取状态描述
const getStatusDescription = (status: number) => {
  const descMap = {
    0: '您的申请已提交，我们将在3个工作日内进行审核',
    1: '我们正在仔细审核您的申请资料，请耐心等待',
    2: '恭喜！您的申请已通过审核，请按要求缴纳押金完成合作',
    3: '很抱歉，您的申请未通过审核，请修改后重新提交'
  }
  return descMap[status] || '状态异常，请联系客服'
}

// 获取合伙人级别名称
const getPartnerLevelName = (levelId: string) => {
  const level = partnerLevels.value.find(l => l.id === levelId)
  return level ? level.name : '未知级别'
}



// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 缴纳押金
const handlePayDeposit = () => {
  if (!selectedLevel.value || selectedLevel.value.deposit === 0) {
    uni.showToast({
      title: '该级别无需缴纳押金',
      icon: 'none'
    })
    return
  }

  const remainingDeposit = selectedLevel.value.deposit - (apply.value.paidDeposit || 0)
  if (remainingDeposit <= 0) {
    uni.showToast({
      title: '押金已缴纳完成',
      icon: 'none'
    })
    return
  }

  // 跳转到押金支付页面
  uni.navigateTo({
    url: `/pages/partner/pay?amount=${remainingDeposit}&type=deposit`
  })
}

// 修改申请
const handleEditApplication = () => {
  uni.navigateTo({
    url: '/pages/partner/apply'
  })
}

// 联系客服
const handleContactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '请拨打客服热线：13296653558\n工作时间：工作日 9:00-18:00',
    showCancel: true,
    cancelText: '取消',
    confirmText: '拨打电话',
    success: (res) => {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: '13296653558'
        })
      }
    }
  })
}

// 初始化数据
const init = async () => {
  try {
    const data = await getPartnerApplyData()
    if (data) {
      apply.value = data

      // 设置时间戳（模拟数据，实际应该从后端获取）
      if (data.createTime) {
        statusSteps.value[0].time = formatDate(data.createTime)
      }
      if (data.reviewTime) {
        statusSteps.value[1].time = formatDate(data.reviewTime)
      }
      if (data.approveTime) {
        statusSteps.value[2].time = formatDate(data.approveTime)
      }
    } else {
      // 如果没有申请数据，跳转到申请页面
      uni.showModal({
        title: '提示',
        content: '未找到申请记录，请先提交申请',
        showCancel: false,
        success: () => {
          uni.redirectTo({
            url: '/pages/partner/apply'
          })
        }
      })
    }
  } catch (error) {
    console.error('获取申请数据失败：', error)
    uni.showToast({
      title: '获取数据失败，请重试',
      icon: 'none'
    })
  }
}

onShow(() => {
  init()
})

</script>


<style scoped lang="scss">
/* uniapp 兼容性样式 */

/* 修复小程序中按钮样式 */
button {
  border: none;
  outline: none;
}

button::after {
  border: none;
}

/* 修复小程序中渐变背景可能不支持的问题 */
.bg-gradient-fallback {
  background: #4f46e5; /* 蓝色备用色 */
}

/* 修复小程序中 transform 兼容性 */
.transform-fix {
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}

/* 修复小程序中阴影效果 */
.shadow-fix {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 修复小程序中圆角边框 */
.rounded-fix {
  border-radius: 12px;
  overflow: hidden;
}

/* 修复小程序中文字渐变可能不支持的问题 */
.text-gradient-fallback {
  color: #4f46e5;
}

/* 小程序特有的安全区域适配 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 修复小程序中 hover 效果 */
.hover-effect:hover {
  opacity: 0.8;
  transition: opacity 0.2s;
}

/* 修复小程序中 grid 布局兼容性 */
@media screen and (max-width: 768px) {
  .grid {
    display: flex;
    flex-direction: column;
  }

  .grid-cols-2 > * {
    width: 100%;
    margin-bottom: 1rem;
  }

  .grid-cols-3 > * {
    width: 100%;
    margin-bottom: 1rem;
  }
}

/* 修复小程序中字体大小单位 */
.text-responsive {
  font-size: 32rpx;
}

@media screen and (min-width: 768px) {
  .text-responsive {
    font-size: 16px;
  }
}

/* 进度条动画 */
.progress-bar {
  transition: width 0.5s ease-in-out;
}

/* 状态步骤样式优化 */
.status-step {
  position: relative;
}

.status-step::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  width: 100%;
  height: 2px;
  background-color: #e5e7eb;
  transform: translateY(-50%);
  z-index: -1;
}

.status-step:last-child::after {
  display: none;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 按钮点击效果 */
.btn-press {
  transition: all 0.1s ease;
}

.btn-press:active {
  transform: scale(0.98);
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 28px;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

/* 响应式文字大小 */
@media screen and (max-width: 640px) {
  .text-3xl {
    font-size: 1.875rem;
  }

  .text-2xl {
    font-size: 1.5rem;
  }

  .text-xl {
    font-size: 1.25rem;
  }

  .text-lg {
    font-size: 1.125rem;
  }
}

/* 修复小程序中flex布局问题 */
.flex-fix {
  display: flex;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

/* 间距调整 */
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* 文字省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
